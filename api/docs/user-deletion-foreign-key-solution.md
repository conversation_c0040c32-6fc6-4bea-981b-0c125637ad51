# User Deletion Foreign Key Constraint Solution

## Problem Summary

The application was encountering foreign key constraint violations when deleting users:
```
"update or delete on table \"users\" violates foreign key constraint \"questions_created_by_users_id_fk\" on table \"questions\""
```

This occurred because the `questions.created_by` foreign key constraint was using `RESTRICT` instead of `SET NULL`, preventing user deletion when they had created questions.

## Solution Overview

We implemented a comprehensive solution that:

1. **Checks constraint status** automatically
2. **Applies fixes** if the migration hasn't been run
3. **<PERSON>les question reassignment** efficiently
4. **Provides detailed logging** and audit trails
5. **Maintains data integrity** through transactions

## Implementation Details

### 1. Enhanced UserDeletionService

The `UserDeletionService` now includes:

- `checkConstraintStatus()` - Verifies current foreign key constraint configuration
- `applyConstraintFixes()` - Manually applies the constraint fixes if needed
- `deleteUserWithQuestionReassignment()` - Enhanced deletion with question handling

### 2. Question Handling Strategies

When deleting a user with questions, you can:

**Option A: Orphan Questions (Default)**
```typescript
await userDeletionService.deleteUserWithQuestionReassignment(userId);
// Questions remain but created_by is set to NULL
```

**Option B: Reassign Questions**
```typescript
await userDeletionService.deleteUserWithQuestionReassignment(
  userId, 
  '<EMAIL>'
);
// Questions are reassigned to the replacement user
```

### 3. Updated Auth Service Methods

The auth service methods now use the enhanced deletion:

```typescript
// Admin deletion with optional question reassignment
await authService.deleteUser(userId, adminId, '<EMAIL>');

// Self-deletion (questions are orphaned)
await authService.deleteOwnAccount(userId, 'DELETE');
```

## Usage Instructions

### Step 1: Check Constraint Status

```typescript
const userDeletionService = app.get(UserDeletionService);
const status = await userDeletionService.checkConstraintStatus();

console.log('Migration Applied:', status.migrationApplied);
console.log('Constraint Type:', status.questionsConstraintType);
console.log('Requires Manual Cleanup:', status.requiresManualCleanup);
```

### Step 2: Apply Fixes if Needed

If `requiresManualCleanup` is `true`:

```typescript
const fixResult = await userDeletionService.applyConstraintFixes();
if (fixResult.success) {
  console.log('Fixes applied:', fixResult.appliedFixes);
} else {
  console.error('Failed to apply fixes:', fixResult.message);
}
```

### Step 3: Delete Users Safely

```typescript
// Delete user with question reassignment
const result = await userDeletionService.deleteUserWithQuestionReassignment(
  'user-id-to-delete',
  '<EMAIL>', // Optional
  true, // Send notification
  'admin-user-id' // Admin performing deletion
);

if (result.success) {
  console.log('Cleanup Stats:', result.cleanupStats);
  console.log('Questions Reassigned:', result.cleanupStats.questionsReassigned);
  console.log('Questions Orphaned:', result.cleanupStats.questionsOrphaned);
}
```

## Testing the Solution

### Run the Test Script

```bash
cd api
npx ts-node src/user-deletion/test-user-deletion.ts
```

This will:
1. Check current constraint status
2. Apply fixes if needed
3. Verify the solution is working

### Manual Testing

1. **Create a test user with questions**
2. **Try deleting the user**
3. **Verify questions are handled correctly**
4. **Check email notifications are sent**

## Migration Alternative

If you prefer to run the migration manually:

```sql
-- Run the migration file
\i migrations/0018_fix-remaining-user-deletion-constraints.sql
```

Or apply individual fixes:

```sql
-- Fix questions.created_by constraint
ALTER TABLE "questions" DROP CONSTRAINT IF EXISTS "questions_created_by_users_id_fk";
ALTER TABLE "questions" ADD CONSTRAINT "questions_created_by_users_id_fk" 
FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") 
ON DELETE SET NULL ON UPDATE NO ACTION;

-- Add quiz.created_by constraint
ALTER TABLE "quiz" DROP CONSTRAINT IF EXISTS "quiz_created_by_users_id_fk";
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_created_by_users_id_fk" 
FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") 
ON DELETE SET NULL ON UPDATE NO ACTION;
```

## Monitoring and Logging

The solution provides comprehensive logging:

```
[UserDeletionService] Checking constraint status...
[UserDeletionService] Questions constraint delete rule: RESTRICT
[UserDeletionService] Migration not applied, using manual cleanup approach
[UserDeletionService] Found replacement user: user-123 (<EMAIL>)
[UserDeletionService] Reassigned 5 <NAME_EMAIL>
[UserDeletionService] Successfully deleted user user-456 with manual cleanup
```

## Email Notifications

Admin users receive detailed email reports including:
- User deletion summary
- Question reassignment details
- Cleanup statistics
- Constraint status information

## Error Handling

The solution handles various error scenarios:
- User not found
- Replacement user not found
- Database constraint violations
- Transaction failures
- Email notification failures

## Performance Considerations

- **Batch Updates**: Questions are updated in efficient batch operations
- **Transactions**: All operations are wrapped in database transactions
- **Logging**: Detailed logging for audit trails
- **Caching**: Cache invalidation after successful deletions

## Security Notes

- Only super_admin users can delete other users
- Users can only delete their own accounts with confirmation
- All deletions are logged and auditable
- Email notifications provide admin oversight

## Troubleshooting

### Common Issues

1. **"Constraint not found" error**
   - Run `checkConstraintStatus()` to verify database state
   - Apply fixes using `applyConstraintFixes()`

2. **"Replacement user not found" error**
   - Verify the replacement user email exists
   - Check email case sensitivity

3. **Transaction timeout**
   - For users with many questions, consider batch processing
   - Monitor database performance during large deletions

### Debug Commands

```typescript
// Check constraint status
const status = await userDeletionService.checkConstraintStatus();

// Apply fixes manually
const fixes = await userDeletionService.applyConstraintFixes();

// Test deletion without actual deletion (dry run)
// Note: Implement dry run mode if needed for testing
```

## Conclusion

This solution provides a robust, production-ready approach to handling foreign key constraint violations during user deletion. It automatically detects and fixes constraint issues while providing comprehensive audit trails and flexible question handling options.
