import { Test, TestingModule } from '@nestjs/testing';
import { UserDeletionService } from './user-deletion.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EmailService } from '@/mail/email.service';
import { QueueService } from '@app/shared/queue/queue.service';
import { NotFoundException } from '@nestjs/common';

describe('UserDeletionService', () => {
  let service: UserDeletionService;
  let mockDrizzleService: jest.Mocked<DrizzleService>;
  let mockEmailService: jest.Mocked<EmailService>;
  let mockQueueService: jest.Mocked<QueueService>;

  const mockUserId = 'test-user-id';
  const mockAdminUserId = 'test-admin-id';
  const mockUser = {
    id: mockUserId,
    email: '<EMAIL>',
    role: 'student',
    state: 'active',
    deleted: false,
    student_profile: {
      id: 'profile-id',
      first_name: 'Test',
      last_name: 'User',
    },
    profile: null,
  };

  beforeEach(async () => {
    const mockDrizzleDb = {
      query: {
        users: {
          findFirst: jest.fn(),
        },
      },
      select: jest.fn(),
      from: jest.fn(),
      where: jest.fn(),
      transaction: jest.fn(),
      delete: jest.fn(),
    };

    mockDrizzleService = {
      db: mockDrizzleDb,
    } as any;

    mockEmailService = {
      sendCustomEmail: jest.fn(),
    } as any;

    mockQueueService = {
      addSingleEmailJob: jest.fn(),
      addBulkEmailJob: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserDeletionService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
      ],
    }).compile();

    service = module.get<UserDeletionService>(UserDeletionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('deleteUser', () => {
    it('should successfully delete a user', async () => {
      // Mock user exists
      mockDrizzleService.db.query.users.findFirst.mockResolvedValueOnce(mockUser);

      // Mock count queries
      const mockSelect = jest.fn().mockReturnThis();
      const mockFrom = jest.fn().mockReturnThis();
      const mockWhere = jest.fn().mockResolvedValue([]);
      
      mockDrizzleService.db.select = mockSelect;
      mockDrizzleService.db.from = mockFrom;
      mockDrizzleService.db.where = mockWhere;

      // Mock transaction
      mockDrizzleService.db.transaction.mockImplementationOnce(async (callback) => {
        const mockTx = {
          select: jest.fn().mockReturnThis(),
          from: jest.fn().mockReturnThis(),
          where: jest.fn().mockResolvedValue([]),
          delete: jest.fn().mockReturnValue({
            where: jest.fn().mockReturnValue({
              returning: jest.fn().mockResolvedValueOnce([
                {
                  id: mockUserId,
                  email: '<EMAIL>',
                  role: 'student',
                },
              ]),
            }),
          }),
        };
        return callback(mockTx);
      });

      const result = await service.deleteUser(mockUserId);

      expect(result.success).toBe(true);
      expect(result.deletedUser).toEqual({
        id: mockUserId,
        email: '<EMAIL>',
        role: 'student',
      });
      expect(result.cleanupStats).toBeDefined();
    });

    it('should throw NotFoundException when user does not exist', async () => {
      mockDrizzleService.db.query.users.findFirst.mockResolvedValueOnce(null);

      const result = await service.deleteUser(mockUserId);

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });

    it('should send email notification when requested', async () => {
      // Mock user exists
      mockDrizzleService.db.query.users.findFirst.mockResolvedValueOnce(mockUser);

      // Mock admin user
      mockDrizzleService.db.query.users.findFirst
        .mockResolvedValueOnceOnCall(1, {
          id: mockAdminUserId,
          email: '<EMAIL>',
          role: 'super_admin',
        });

      // Mock admin users query
      const mockSelect = jest.fn().mockReturnThis();
      const mockFrom = jest.fn().mockReturnThis();
      const mockWhere = jest.fn().mockResolvedValue([
        { email: '<EMAIL>' },
      ]);
      
      mockDrizzleService.db.select = mockSelect;
      mockDrizzleService.db.from = mockFrom;
      mockDrizzleService.db.where = mockWhere;

      // Mock transaction
      mockDrizzleService.db.transaction.mockImplementationOnce(async (callback) => {
        const mockTx = {
          select: jest.fn().mockReturnThis(),
          from: jest.fn().mockReturnThis(),
          where: jest.fn().mockResolvedValue([]),
          delete: jest.fn().mockReturnValue({
            where: jest.fn().mockReturnValue({
              returning: jest.fn().mockResolvedValueOnce([
                {
                  id: mockUserId,
                  email: '<EMAIL>',
                  role: 'student',
                },
              ]),
            }),
          }),
        };
        return callback(mockTx);
      });

      const result = await service.deleteUser(mockUserId, true, mockAdminUserId);

      expect(result.success).toBe(true);
      expect(mockEmailService.sendCustomEmail).toHaveBeenCalledWith({
        email: '<EMAIL>',
        subject: '🗑️ User Deletion Report - Operation Complete',
        template: 'user-deletion-report',
        context: expect.any(Object),
      });
    });
  });

  describe('bulkDeleteUsers', () => {
    const mockUserIds = ['user1', 'user2', 'user3'];
    const mockExistingUsers = [
      { id: 'user1', email: '<EMAIL>', role: 'student' },
      { id: 'user2', email: '<EMAIL>', role: 'student' },
      { id: 'user3', email: '<EMAIL>', role: 'student' },
    ];

    it('should successfully delete multiple users', async () => {
      // Mock existing users query
      const mockSelect = jest.fn().mockReturnThis();
      const mockFrom = jest.fn().mockReturnThis();
      const mockWhere = jest.fn().mockResolvedValue(mockExistingUsers);
      
      mockDrizzleService.db.select = mockSelect;
      mockDrizzleService.db.from = mockFrom;
      mockDrizzleService.db.where = mockWhere;

      // Mock individual user deletions
      jest.spyOn(service, 'deleteUser').mockResolvedValue({
        success: true,
        deletedUser: { id: 'user1', email: '<EMAIL>', role: 'student' },
        cleanupStats: {
          questionsOrphaned: 0,
          quizzesOrphaned: 0,
          organisationsOrphaned: 0,
          pointsConfigOrphaned: 0,
          notificationTemplatesOrphaned: 0,
          scheduledNotificationsOrphaned: 0,
          rafflesOrphaned: 0,
          clubsOrphaned: 0,
          studentProfileDeleted: true,
          postsDeleted: 0,
        },
      });

      const result = await service.bulkDeleteUsers(mockUserIds, mockAdminUserId);

      expect(result.totalProcessed).toBe(3);
      expect(result.successCount).toBe(3);
      expect(result.failureCount).toBe(0);
      expect(result.successes).toHaveLength(3);
      expect(result.failures).toHaveLength(0);
    });

    it('should handle partial failures in bulk deletion', async () => {
      // Mock existing users query
      const mockSelect = jest.fn().mockReturnThis();
      const mockFrom = jest.fn().mockReturnThis();
      const mockWhere = jest.fn().mockResolvedValue(mockExistingUsers);
      
      mockDrizzleService.db.select = mockSelect;
      mockDrizzleService.db.from = mockFrom;
      mockDrizzleService.db.where = mockWhere;

      // Mock individual user deletions with some failures
      jest.spyOn(service, 'deleteUser')
        .mockResolvedValueOnce({
          success: true,
          deletedUser: { id: 'user1', email: '<EMAIL>', role: 'student' },
          cleanupStats: {
            questionsOrphaned: 0,
            quizzesOrphaned: 0,
            organisationsOrphaned: 0,
            pointsConfigOrphaned: 0,
            notificationTemplatesOrphaned: 0,
            scheduledNotificationsOrphaned: 0,
            rafflesOrphaned: 0,
            clubsOrphaned: 0,
            studentProfileDeleted: true,
            postsDeleted: 0,
          },
        })
        .mockResolvedValueOnce({
          success: false,
          error: 'Database constraint violation',
        })
        .mockResolvedValueOnce({
          success: true,
          deletedUser: { id: 'user3', email: '<EMAIL>', role: 'student' },
          cleanupStats: {
            questionsOrphaned: 0,
            quizzesOrphaned: 0,
            organisationsOrphaned: 0,
            pointsConfigOrphaned: 0,
            notificationTemplatesOrphaned: 0,
            scheduledNotificationsOrphaned: 0,
            rafflesOrphaned: 0,
            clubsOrphaned: 0,
            studentProfileDeleted: true,
            postsDeleted: 0,
          },
        });

      const result = await service.bulkDeleteUsers(mockUserIds, mockAdminUserId);

      expect(result.totalProcessed).toBe(3);
      expect(result.successCount).toBe(2);
      expect(result.failureCount).toBe(1);
      expect(result.successes).toHaveLength(2);
      expect(result.failures).toHaveLength(1);
      expect(result.failures[0].error).toBe('Database constraint violation');
    });

    it('should return empty result when no users exist', async () => {
      // Mock empty users query
      const mockSelect = jest.fn().mockReturnThis();
      const mockFrom = jest.fn().mockReturnThis();
      const mockWhere = jest.fn().mockResolvedValue([]);
      
      mockDrizzleService.db.select = mockSelect;
      mockDrizzleService.db.from = mockFrom;
      mockDrizzleService.db.where = mockWhere;

      const result = await service.bulkDeleteUsers(mockUserIds, mockAdminUserId);

      expect(result.totalProcessed).toBe(0);
      expect(result.successCount).toBe(0);
      expect(result.failureCount).toBe(0);
    });
  });
});
