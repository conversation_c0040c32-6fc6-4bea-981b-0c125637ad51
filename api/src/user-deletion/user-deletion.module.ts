import { Module } from '@nestjs/common';
import { UserDeletionService } from './user-deletion.service';
import { EmailModule } from '@/mail/email.module';
import { QueueModule } from '@app/shared/queue/queue.module';
import { ConfigurableDatabaseModule } from '@app/shared/drizzle/drizzle.module';

@Module({
  imports: [
    EmailModule,
    QueueModule.register(),
    ConfigurableDatabaseModule.register({
      connectionString:
        process.env.DATABASE_URL ||
        'postgresql://postgres:postgres@localhost:5432/postgres',
    }),
  ],
  providers: [UserDeletionService],
  exports: [UserDeletionService],
})
export class UserDeletionModule {}
