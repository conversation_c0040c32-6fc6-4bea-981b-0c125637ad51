import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  users,
  student_profiles,
  posts,
  questionsSchema,
  quizSchema,
  organisations,
  pointsConfigSchema,
  notification_templates,
  scheduled_notifications,
  raffleSchema,
  student_clubs,
} from '@/db/schema';
import { eq, and, inArray } from 'drizzle-orm';
import { EmailService } from '@/mail/email.service';
import { QueueService } from '@app/shared/queue/queue.service';

export interface UserDeletionResult {
  success: boolean;
  deletedUser?: {
    id: string;
    email: string;
    role: string;
  };
  cleanupStats?: {
    questionsOrphaned: number;
    quizzesOrphaned: number;
    organisationsOrphaned: number;
    pointsConfigOrphaned: number;
    notificationTemplatesOrphaned: number;
    scheduledNotificationsOrphaned: number;
    rafflesOrphaned: number;
    clubsOrphaned: number;
    studentProfileDeleted: boolean;
    postsDeleted: number;
  };
  error?: string;
}

export interface BulkUserDeletionResult {
  totalProcessed: number;
  successCount: number;
  failureCount: number;
  successes: Array<{
    id: string;
    email: string;
    role: string;
    cleanupStats: UserDeletionResult['cleanupStats'];
  }>;
  failures: Array<{
    id: string;
    email: string;
    error: string;
  }>;
  duration: string;
  startTime: Date;
  endTime: Date;
}

@Injectable()
export class UserDeletionService {
  private readonly logger = new Logger(UserDeletionService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly emailService: EmailService,
    private readonly queueService: QueueService,
  ) {}

  /**
   * Delete a single user with comprehensive cleanup and logging
   */
  async deleteUser(userId: string): Promise<UserDeletionResult> {
    try {
      // First, check if user exists and get their information
      const userToDelete = await this.drizzle.db.query.users.findFirst({
        where: eq(users.id, userId),
        with: {
          student_profile: true,
          profile: true,
        },
      });

      if (!userToDelete) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Perform deletion in a transaction with comprehensive cleanup
      const deletionResult = await this.drizzle.db.transaction(async (tx) => {
        this.logger.log(
          `Starting comprehensive deletion for user ${userId} (${userToDelete.email})`,
        );

        // Step 1: Count records that will be orphaned (SET NULL)
        const questionsCount = await tx
          .select()
          .from(questionsSchema)
          .where(eq(questionsSchema.created_by, userId));

        const quizzesCount = await tx
          .select()
          .from(quizSchema)
          .where(eq(quizSchema.created_by, userId));

        const organisationsCount = await tx
          .select()
          .from(organisations)
          .where(eq(organisations.user_id, userId));

        const pointsConfigCount = await tx
          .select()
          .from(pointsConfigSchema)
          .where(eq(pointsConfigSchema.created_by, userId));

        const notificationTemplatesCount = await tx
          .select()
          .from(notification_templates)
          .where(eq(notification_templates.created_by, userId));

        const scheduledNotificationsCount = await tx
          .select()
          .from(scheduled_notifications)
          .where(eq(scheduled_notifications.created_by, userId));

        const rafflesCount = await tx
          .select()
          .from(raffleSchema)
          .where(eq(raffleSchema.createdBy, userId));

        const clubsCount = await tx
          .select()
          .from(student_clubs)
          .where(eq(student_clubs.club_admin, userId));

        // Step 2: Count records that will be deleted (CASCADE)
        const postsCount = await tx
          .select()
          .from(posts)
          .where(eq(posts.postedBy, userId));

        // Step 3: Delete the user (this will trigger all CASCADE and SET NULL operations)
        const deletedUser = await tx
          .delete(users)
          .where(eq(users.id, userId))
          .returning({
            id: users.id,
            email: users.email,
            role: users.role,
          });

        if (!deletedUser.length) {
          throw new Error('Failed to delete user');
        }

        this.logger.log(
          `Successfully deleted user ${userId} with comprehensive cleanup`,
        );

        return {
          deletedUser: deletedUser[0],
          cleanupStats: {
            questionsOrphaned: questionsCount.length,
            quizzesOrphaned: quizzesCount.length,
            organisationsOrphaned: organisationsCount.length,
            pointsConfigOrphaned: pointsConfigCount.length,
            notificationTemplatesOrphaned: notificationTemplatesCount.length,
            scheduledNotificationsOrphaned: scheduledNotificationsCount.length,
            rafflesOrphaned: rafflesCount.length,
            clubsOrphaned: clubsCount.length,
            studentProfileDeleted: !!userToDelete.student_profile,
            postsDeleted: postsCount.length,
          },
        };
      });

      return {
        success: true,
        deletedUser: deletionResult.deletedUser,
        cleanupStats: deletionResult.cleanupStats,
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to delete user ${userId}: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Delete multiple users in bulk with comprehensive reporting
   */
  async bulkDeleteUsers(
    userIds: string[],
    adminUserId: string,
  ): Promise<BulkUserDeletionResult> {
    const startTime = new Date();
    const successes: BulkUserDeletionResult['successes'] = [];
    const failures: BulkUserDeletionResult['failures'] = [];

    this.logger.log(
      `Starting bulk deletion of ${userIds.length} users by admin ${adminUserId}`,
    );

    // Validate that all user IDs exist first
    const existingUsers = await this.drizzle.db
      .select({ id: users.id, email: users.email, role: users.role })
      .from(users)
      .where(and(inArray(users.id, userIds), eq(users.deleted, false)));

    if (existingUsers.length === 0) {
      const endTime = new Date();
      return {
        totalProcessed: 0,
        successCount: 0,
        failureCount: 0,
        successes: [],
        failures: [],
        duration: this.calculateDuration(startTime, endTime),
        startTime,
        endTime,
      };
    }

    // Process each user deletion
    for (const user of existingUsers) {
      try {
        const result = await this.deleteUser(user.id);

        if (result.success && result.deletedUser && result.cleanupStats) {
          successes.push({
            id: result.deletedUser.id,
            email: result.deletedUser.email,
            role: result.deletedUser.role,
            cleanupStats: result.cleanupStats,
          });
        } else {
          failures.push({
            id: user.id,
            email: user.email,
            error: result.error || 'Unknown error occurred',
          });
        }
      } catch (error: any) {
        failures.push({
          id: user.id,
          email: user.email,
          error: error.message,
        });
      }
    }

    const endTime = new Date();
    const bulkResult: BulkUserDeletionResult = {
      totalProcessed: existingUsers.length,
      successCount: successes.length,
      failureCount: failures.length,
      successes,
      failures,
      duration: this.calculateDuration(startTime, endTime),
      startTime,
      endTime,
    };

    // Send email notification to admin users
    await this.sendBulkDeletionNotification(bulkResult, adminUserId);

    this.logger.log(
      `Bulk deletion completed: ${successes.length} successful, ${failures.length} failed out of ${existingUsers.length} total`,
    );

    return bulkResult;
  }

  /**
   * Send email notification about bulk deletion results
   */
  private async sendBulkDeletionNotification(
    result: BulkUserDeletionResult,
    adminUserId: string,
  ): Promise<void> {
    try {
      // Get admin user details
      const adminUser = await this.drizzle.db.query.users.findFirst({
        where: eq(users.id, adminUserId),
      });

      if (!adminUser) {
        this.logger.warn(
          `Admin user ${adminUserId} not found for email notification`,
        );
        return;
      }

      // Get all admin users for notification
      const adminUsers = await this.drizzle.db
        .select({ email: users.email })
        .from(users)
        .where(
          and(
            eq(users.role, 'super_admin'),
            eq(users.state, 'active'),
            eq(users.deleted, false),
          ),
        );

      if (adminUsers.length === 0) {
        this.logger.warn(
          'No active admin users found for bulk deletion notification',
        );
        return;
      }

      const emailContext = {
        adminName: adminUser.email.split('@')[0],
        totalProcessed: result.totalProcessed,
        successCount: result.successCount,
        failureCount: result.failureCount,
        duration: result.duration,
        completedAt: result.endTime.toLocaleString(),
        failures: result.failures,
      };

      // Send emails to all admin users
      for (const admin of adminUsers) {
        await this.emailService.sendCustomEmail({
          email: admin.email,
          subject: '🗑️ Bulk User Deletion Report - Operation Complete',
          template: 'bulk-deletion-report',
          context: emailContext,
        });
      }

      this.logger.log(
        `Sent bulk deletion notification to ${adminUsers.length} admin users`,
      );
    } catch (error: any) {
      this.logger.error(
        `Failed to send bulk deletion notification: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Calculate duration between two dates in human-readable format
   */
  private calculateDuration(startTime: Date, endTime: Date): string {
    const durationMs = endTime.getTime() - startTime.getTime();
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);

    if (minutes > 0) {
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    }

    return `${seconds}s`;
  }
}
